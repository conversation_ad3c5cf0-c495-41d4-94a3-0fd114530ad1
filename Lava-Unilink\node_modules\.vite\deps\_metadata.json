{"hash": "1b3b412f", "configHash": "5ebd54f0", "lockfileHash": "463cbff9", "browserHash": "495cfe92", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "a3d1774e", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "37fe74be", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "03f7ca36", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4e209a02", "needsInterop": true}, "@inertiajs/react": {"src": "../../@inertiajs/react/dist/index.esm.js", "file": "@inertiajs_react.js", "fileHash": "532abc14", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "e7c39047", "needsInterop": false}, "laravel-vite-plugin/inertia-helpers": {"src": "../../laravel-vite-plugin/inertia-helpers/index.js", "file": "laravel-vite-plugin_inertia-helpers.js", "fileHash": "f405ecec", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "973f4215", "needsInterop": true}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "f40b8556", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "8d838d3b", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "0dcbe47f", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "d621451b", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "02b99cd0", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "68d9791c", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "6f080b3c", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "3678e5ff", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "0edc4d06", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "950e90d9", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "002d3e32", "needsInterop": false}}, "chunks": {"chunk-64SUKHD5": {"file": "chunk-64SUKHD5.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-UTIEE34H": {"file": "chunk-UTIEE34H.js"}, "chunk-HTOPQGJL": {"file": "chunk-HTOPQGJL.js"}, "chunk-MMRGZLDN": {"file": "chunk-MMRGZLDN.js"}, "chunk-5QB6WZBA": {"file": "chunk-5QB6WZBA.js"}, "chunk-A4XG3SSV": {"file": "chunk-A4XG3SSV.js"}, "chunk-S6O7WHS2": {"file": "chunk-S6O7WHS2.js"}, "chunk-Q5B56BVT": {"file": "chunk-Q5B56BVT.js"}, "chunk-3M4ZFO5U": {"file": "chunk-3M4ZFO5U.js"}, "chunk-YJYY6GXC": {"file": "chunk-YJYY6GXC.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}