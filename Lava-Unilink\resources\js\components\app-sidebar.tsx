import AppLogo from '@/components/app-logo';
import { NavMain } from '@/components/nav-main';
import { Sidebar, SidebarContent, SidebarHeader } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Bell, Calendar, Home, MessageSquare, Settings, Users, Rss } from 'lucide-react';

const data: { navMain: NavItem[] } = {
    navMain: [
        { title: 'Dashboard', href: '/dashboard', icon: Home },
        { title: 'Feed', href: '/feed', icon: Rss },
        { title: 'Announcements', href: '/announcements', icon: Bell },
        { title: 'Events', href: '/events', icon: Calendar },
        { title: 'Organizations', href: '/organizations', icon: Users },
        { title: 'Messages', href: '/messages', icon: MessageSquare },
        { title: 'Settings', href: '/settings', icon: Settings },
    ],
};

export function AppSidebar() {
    return (
        <Sidebar variant="inset">
            <SidebarHeader>
                <div className="flex items-center gap-2 px-2 py-2">
                    <AppLogo />
                </div>
            </SidebarHeader>
            <SidebarContent>
                <NavMain items={data.navMain} />
            </SidebarContent>
        </Sidebar>
    );
}



