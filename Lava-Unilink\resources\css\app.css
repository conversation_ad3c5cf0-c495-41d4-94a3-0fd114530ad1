@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 93%;
    --foreground: 222 14% 14%;

    --primary: 101 52% 54%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 10% 25%;
    --secondary-foreground: 0 0% 100%;

    --muted: 220 10% 90%;
    --muted-foreground: 220 10% 40%;

    --accent: 101 52% 54%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 10% 85%;
    --input: 220 10% 85%;
    --ring: 101 52% 54%;

    --radius: 0.5rem;

    /* Sidebar variables */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 222 14% 14%;
    --sidebar-primary: 101 52% 54%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 101 52% 54%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 220 10% 85%;
    --sidebar-ring: 101 52% 54%;

    /* Card variables */
    --card: 0 0% 100%;
    --card-foreground: 222 14% 14%;
  }

  .dark {
    --background: 222 14% 14%;
    --foreground: 0 0% 93%;

    --primary: 101 52% 54%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 10% 25%;
    --secondary-foreground: 0 0% 100%;

    --muted: 220 10% 20%;
    --muted-foreground: 220 10% 70%;

    --accent: 101 52% 54%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 10% 25%;
    --input: 220 10% 25%;
    --ring: 101 52% 54%;

    /* Sidebar variables for dark mode */
    --sidebar-background: 222 14% 14%;
    --sidebar-foreground: 0 0% 93%;
    --sidebar-primary: 101 52% 54%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 101 52% 54%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 220 10% 25%;
    --sidebar-ring: 101 52% 54%;

    /* Card variables for dark mode */
    --card: 220 10% 25%;
    --card-foreground: 0 0% 93%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

