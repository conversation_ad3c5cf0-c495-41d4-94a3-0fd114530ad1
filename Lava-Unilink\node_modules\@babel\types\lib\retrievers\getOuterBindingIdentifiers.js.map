{"version": 3, "names": ["_getBindingIdentifiers", "require", "_default", "exports", "default", "getOuterBindingIdentifiers", "node", "duplicates", "getBindingIdentifiers"], "sources": ["../../src/retrievers/getOuterBindingIdentifiers.ts"], "sourcesContent": ["import getBindingIdentifiers from \"./getBindingIdentifiers.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default getOuterBindingIdentifiers as {\n  (node: t.Node, duplicates: true): Record<string, Array<t.Identifier>>;\n  (node: t.Node, duplicates?: false): Record<string, t.Identifier>;\n  (\n    node: t.Node,\n    duplicates?: boolean,\n  ): Record<string, t.Identifier> | Record<string, Array<t.Identifier>>;\n};\n\nfunction getOuterBindingIdentifiers(\n  node: t.Node,\n  duplicates: boolean,\n): Record<string, t.Identifier> | Record<string, Array<t.Identifier>> {\n  return getBindingIdentifiers(node, duplicates, true);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,sBAAA,GAAAC,OAAA;AAA+D,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGhDC,0BAA0B;AASzC,SAASA,0BAA0BA,CACjCC,IAAY,EACZC,UAAmB,EACiD;EACpE,OAAO,IAAAC,8BAAqB,EAACF,IAAI,EAAEC,UAAU,EAAE,IAAI,CAAC;AACtD", "ignoreList": []}