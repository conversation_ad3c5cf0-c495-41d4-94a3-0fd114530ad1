import { SidebarProvider } from '@/components/ui/sidebar';
import { cn } from '@/lib/utils';
import { type PropsWithChildren } from 'react';

interface AppShellProps extends PropsWithChildren {
    variant?: 'default' | 'sidebar';
}

export function AppShell({ children, variant = 'default' }: AppShellProps) {
    if (variant === 'sidebar') {
        return (
            <SidebarProvider>
                <div className={cn('flex min-h-screen w-full')}>
                    {children}
                </div>
            </SidebarProvider>
        );
    }

    return (
        <div
            className={cn(
                'flex min-h-screen flex-col bg-unilink-lightest dark:bg-unilink-darkest'
            )}
        >
            {children}
        </div>
    );
}

