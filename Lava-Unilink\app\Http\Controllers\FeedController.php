<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class FeedController extends Controller
{
    /**
     * Display the personalized feed.
     */
    public function index(Request $request): Response
    {
        // For now, return sample data. In production, this would fetch from database
        $posts = $this->getSamplePosts();
        
        return Inertia::render('feed', [
            'initialPosts' => [
                'data' => $posts,
                'current_page' => 1,
                'last_page' => 1,
                'total' => count($posts)
            ],
            'feedType' => 'personalized'
        ]);
    }

    /**
     * Display the trending feed.
     */
    public function trending(Request $request): Response
    {
        // For now, return sample data. In production, this would fetch trending posts
        $posts = $this->getSamplePosts();
        
        return Inertia::render('feed', [
            'initialPosts' => [
                'data' => $posts,
                'current_page' => 1,
                'last_page' => 1,
                'total' => count($posts)
            ],
            'feedType' => 'trending'
        ]);
    }

    /**
     * Get sample posts for demonstration.
     * In production, this would be replaced with actual database queries.
     */
    private function getSamplePosts(): array
    {
        return [
            [
                'id' => 1,
                'title' => 'Welcome to the New Academic Year!',
                'content' => "We're excited to welcome all students back to campus for another amazing year of learning and growth. This year brings new opportunities, new challenges, and new friendships waiting to be made.\n\nDon't forget to check out the orientation activities happening throughout the week. We have exciting events planned including campus tours, club fairs, and meet-and-greet sessions with faculty.\n\nLet's make this academic year the best one yet! 🎓✨",
                'type' => 'announcement',
                'visibility' => 'public',
                'is_pinned' => true,
                'comments_enabled' => true,
                'user' => [
                    'id' => 1,
                    'name' => 'University Admin',
                    'avatar' => null
                ],
                'organization' => [
                    'id' => 1,
                    'name' => 'SKSU Official',
                    'logo' => null
                ],
                'comments_count' => 24,
                'reactions_count' => 156,
                'user_reaction' => null,
                'created_at' => '2024-01-15T08:00:00Z',
                'published_at' => '2024-01-15T08:00:00Z'
            ],
            [
                'id' => 2,
                'title' => 'Computer Science Club Meeting',
                'content' => "Join us for our first meeting of the semester! We'll be discussing upcoming projects, hackathons, and workshops.\n\n📅 Date: January 20, 2024\n🕐 Time: 3:00 PM - 5:00 PM\n📍 Location: CS Building, Room 201\n\nTopics to cover:\n- Introduction to new members\n- Project showcase planning\n- Upcoming coding competitions\n- Workshop schedule for the semester\n\nRefreshments will be provided! See you there! 🚀",
                'type' => 'event',
                'visibility' => 'public',
                'is_pinned' => false,
                'comments_enabled' => true,
                'user' => [
                    'id' => 2,
                    'name' => 'Sarah Chen',
                    'avatar' => null
                ],
                'organization' => [
                    'id' => 2,
                    'name' => 'CS Club',
                    'logo' => null
                ],
                'comments_count' => 8,
                'reactions_count' => 42,
                'user_reaction' => 'like',
                'created_at' => '2024-01-14T14:30:00Z',
                'published_at' => '2024-01-14T14:30:00Z'
            ],
            [
                'id' => 3,
                'title' => 'Study Group for Advanced Mathematics',
                'content' => "Looking for motivated students to join our advanced mathematics study group. We meet twice a week to work through challenging problems and prepare for exams together.\n\nWe cover topics including:\n- Calculus III\n- Linear Algebra\n- Differential Equations\n- Statistics\n\nIf you're interested in joining or have questions, feel free to comment below or send me a message!\n\nStudy sessions are held every Tuesday and Thursday from 6:00 PM - 8:00 PM in the Math Building, Room 105.",
                'type' => 'discussion',
                'visibility' => 'public',
                'is_pinned' => false,
                'comments_enabled' => true,
                'user' => [
                    'id' => 3,
                    'name' => 'Michael Rodriguez',
                    'avatar' => null
                ],
                'organization' => null,
                'comments_count' => 12,
                'reactions_count' => 28,
                'user_reaction' => null,
                'created_at' => '2024-01-13T16:45:00Z',
                'published_at' => '2024-01-13T16:45:00Z'
            ],
            [
                'id' => 4,
                'title' => 'Library Extended Hours During Finals',
                'content' => "Good news for all students! The university library will be extending its operating hours during the finals period.\n\n📚 Extended Hours:\n- Monday - Thursday: 7:00 AM - 12:00 AM\n- Friday - Saturday: 7:00 AM - 10:00 PM\n- Sunday: 9:00 AM - 12:00 AM\n\nAdditional study rooms and computer labs will also be available. Group study rooms can be reserved online through the library portal.\n\nWe'll also have extended WiFi coverage and additional power outlets installed throughout the building.\n\nGood luck with your studies! 📖✨",
                'type' => 'announcement',
                'visibility' => 'public',
                'is_pinned' => false,
                'comments_enabled' => true,
                'user' => [
                    'id' => 4,
                    'name' => 'Library Services',
                    'avatar' => null
                ],
                'organization' => [
                    'id' => 3,
                    'name' => 'University Library',
                    'logo' => null
                ],
                'comments_count' => 6,
                'reactions_count' => 89,
                'user_reaction' => null,
                'created_at' => '2024-01-12T10:15:00Z',
                'published_at' => '2024-01-12T10:15:00Z'
            ],
            [
                'id' => 5,
                'title' => 'Basketball Tournament Registration Open',
                'content' => "The annual inter-college basketball tournament is here! Registration is now open for all interested teams.\n\n🏀 Tournament Details:\n- Registration Deadline: January 25, 2024\n- Tournament Dates: February 5-15, 2024\n- Entry Fee: ₱500 per team\n- Maximum 12 players per team\n\nPrizes:\n🥇 1st Place: ₱15,000 + Trophy\n🥈 2nd Place: ₱10,000 + Trophy\n🥉 3rd Place: ₱5,000 + Trophy\n\nTo register, visit the Sports Office or contact <NAME_EMAIL>\n\nLet's see which college has the best ballers! 🔥",
                'type' => 'event',
                'visibility' => 'public',
                'is_pinned' => false,
                'comments_enabled' => true,
                'user' => [
                    'id' => 5,
                    'name' => 'Sports Office',
                    'avatar' => null
                ],
                'organization' => [
                    'id' => 4,
                    'name' => 'SKSU Sports',
                    'logo' => null
                ],
                'comments_count' => 18,
                'reactions_count' => 67,
                'user_reaction' => null,
                'created_at' => '2024-01-11T09:30:00Z',
                'published_at' => '2024-01-11T09:30:00Z'
            ]
        ];
    }
}
