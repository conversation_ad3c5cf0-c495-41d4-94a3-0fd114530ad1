import { useState } from 'react';
import { <PERSON> } from '@inertiajs/react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
    Heart, 
    MessageCircle, 
    Share2, 
    MoreHorizontal, 
    Pin,
    Calendar,
    Users
} from 'lucide-react';

interface User {
    id: number;
    name: string;
    avatar?: string;
}

interface Organization {
    id: number;
    name: string;
    logo?: string;
}

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    media?: Array<{
        path: string;
        name: string;
        type: string;
    }>;
    user: User;
    organization?: Organization;
    comments_count: number;
    reactions_count: number;
    created_at: string;
    published_at: string;
}

interface PostCardProps {
    post: Post;
    onReact?: (postId: number, reactionType: string) => void;
    onComment?: (postId: number) => void;
    onShare?: (postId: number) => void;
}

export default function PostCard({ post, onReact, onComment, onShare }: PostCardProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const [showFullContent, setShowFullContent] = useState(false);
    const [userReaction, setUserReaction] = useState<string | null>(null);

    const getTypeColor = (type: string) => {
        switch (type.toLowerCase()) {
            case 'announcement': return 'bg-unilink-primary text-white';
            case 'event': return 'bg-blue-500 text-white';
            case 'discussion': return 'bg-green-500 text-white';
            case 'news': return 'bg-orange-500 text-white';
            default: return 'bg-unilink-secondary text-white';
        }
    };

    const shouldShowReadMore = post.content.length > 300;

    const truncateContent = (content: string) => {
        return content.length > 300 ? content.substring(0, 300) + '...' : content;
    };

    const getVisibilityIcon = (visibility: string) => {
        switch (visibility) {
            case 'public': return '🌐';
            case 'members_only': return '👥';
            case 'private': return '🔒';
            default: return '🌐';
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

        if (diffInHours < 1) {
            const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
            return `${diffInMinutes}m ago`;
        } else if (diffInHours < 24) {
            return `${diffInHours}h ago`;
        } else if (diffInHours < 168) { // 7 days
            const diffInDays = Math.floor(diffInHours / 24);
            return `${diffInDays}d ago`;
        } else {
            return date.toLocaleDateString();
        }
    };

    const handleReaction = (reactionType: string) => {
        if (onReact) {
            onReact(post.id, reactionType);
            setUserReaction(userReaction === reactionType ? null : reactionType);
        }
    };



    return (
        <Card className="shadow-sm mb-4">
            <CardHeader className="bg-unilink-lightest dark:bg-unilink-secondary">
                <div className="flex justify-between items-center">
                    <div className="flex items-center">
                        <Avatar className="w-10 h-10 mr-2">
                            <AvatarImage
                                src={post.user.avatar ? `/storage/${post.user.avatar}` : undefined}
                                alt={post.user.name}
                            />
                            <AvatarFallback className="bg-unilink-primary text-white">
                                {post.user.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </AvatarFallback>
                        </Avatar>
                        <div>
                            <h5 className="text-lg font-semibold mb-0 text-unilink-darkest dark:text-unilink-lightest">{post.title}</h5>
                            <small className="text-unilink-secondary dark:text-unilink-lightest">
                                Posted by {post.user.name} on {formatDate(post.published_at || post.created_at)}
                                {post.visibility !== 'public' && (
                                    <span className="ml-2">
                                        <i className="fas fa-lock text-unilink-secondary"></i>
                                        {post.visibility === 'organization' ? ' Organization Only' : ' Campus Only'}
                                    </span>
                                )}
                            </small>
                        </div>
                    </div>
                    {post.organization && (
                        <Badge className="bg-unilink-primary text-white">{post.organization.name}</Badge>
                    )}
                </div>
            </CardHeader>

            <CardContent>
                <div className="space-y-4">
                    {/* Post Content */}
                    <div className="text-unilink-secondary dark:text-unilink-lightest">
                        <p className="whitespace-pre-wrap">
                            {showFullContent ? post.content : truncateContent(post.content)}
                        </p>
                        {shouldShowReadMore && (
                            <button
                                onClick={() => setShowFullContent(!showFullContent)}
                                className="text-unilink-primary hover:underline text-sm mt-2"
                            >
                                {showFullContent ? 'Show Less' : 'Read More'}
                            </button>
                        )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                        <Badge className={getTypeColor(post.type)}>
                            {post.type}
                        </Badge>
                        <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                        </Button>
                    </div>
                </div>
            </CardHeader>

            <CardContent className="pt-0">
                <Link href={`/posts/${post.id}`} className="block">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 hover:text-blue-600 transition-colors">
                        {post.title}
                    </h3>
                </Link>

                <div className="text-unilink-secondary dark:text-unilink-lightest mb-4">
                    <p className="whitespace-pre-wrap">
                        {isExpanded ? post.content : truncateContent(post.content)}
                    </p>

                    {shouldShowReadMore && (
                        <button
                            onClick={() => setIsExpanded(!isExpanded)}
                            className="text-unilink-primary hover:text-unilink-primary/80 text-sm font-medium mt-1"
                        >
                            {isExpanded ? 'Show less' : 'Read more'}
                        </button>
                    )}
                </div>

                {/* Media */}
                {post.media && post.media.length > 0 && (
                    <div className="mb-4">
                        <div className="grid grid-cols-2 gap-2">
                            {post.media.slice(0, 4).map((media, index) => (
                                <div key={index} className="relative">
                                    {media.type.startsWith('image/') ? (
                                        <img
                                            src={`/storage/${media.path}`}
                                            alt={media.name}
                                            className="w-full h-32 object-cover rounded-lg"
                                        />
                                    ) : (
                                        <div className="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center">
                                            <div className="text-center">
                                                <div className="text-2xl mb-1">📄</div>
                                                <p className="text-xs text-gray-600 truncate px-2">
                                                    {media.name}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                    {index === 3 && post.media.length > 4 && (
                                        <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                                            <span className="text-white font-semibold">
                                                +{post.media.length - 4} more
                                            </span>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-3 border-t border-unilink-lightest dark:border-unilink-secondary">
                    <div className="flex items-center space-x-4">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReaction('like')}
                            className={`flex items-center space-x-1 ${
                                userReaction === 'like' ? 'text-red-500 hover:text-red-600' : 'text-unilink-secondary hover:text-red-500'
                            }`}
                        >
                            <Heart className={`w-4 h-4 ${userReaction === 'like' ? 'fill-current' : ''}`} />
                            <span>{post.reactions_count}</span>
                        </Button>

                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onComment?.(post.id)}
                            className="flex items-center space-x-1 text-unilink-secondary hover:text-unilink-primary"
                            disabled={!post.comments_enabled}
                        >
                            <MessageCircle className="w-4 h-4" />
                            <span>{post.comments_count}</span>
                        </Button>

                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onShare?.(post.id)}
                            className="flex items-center space-x-1 text-unilink-secondary hover:text-unilink-primary"
                        >
                            <Share2 className="w-4 h-4" />
                            <span>Share</span>
                        </Button>
                    </div>

                    {post.type === 'event' && (
                        <div className="flex items-center text-xs text-gray-500">
                            <Calendar className="w-3 h-3 mr-1" />
                            Event
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
