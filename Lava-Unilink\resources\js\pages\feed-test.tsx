import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';

interface FeedProps {
    initialPosts?: {
        data: any[];
        current_page: number;
        last_page: number;
        total: number;
    };
    feedType?: 'personalized' | 'trending' | 'all';
}

function FeedTest({ initialPosts, feedType = 'personalized' }: FeedProps) {
    console.log('FeedTest component rendered with:', { initialPosts, feedType });

    return (
        <AppLayout>
            <Head title="Feed Test" />
            
            <div className="container mx-auto p-4">
                <h1 className="text-2xl font-bold mb-4">Feed Test Page</h1>
                <p>Feed Type: {feedType}</p>
                <p>Posts Count: {initialPosts?.data?.length || 0}</p>
                
                {initialPosts?.data && initialPosts.data.length > 0 ? (
                    <div className="mt-4">
                        <h2 className="text-xl font-semibold mb-2">Posts:</h2>
                        {initialPosts.data.map((post: any) => (
                            <div key={post.id} className="border p-4 mb-4 rounded">
                                <h3 className="font-bold">{post.title}</h3>
                                <p className="text-gray-600">{post.content.substring(0, 100)}...</p>
                                <p className="text-sm text-gray-500">By: {post.user.name}</p>
                            </div>
                        ))}
                    </div>
                ) : (
                    <p className="mt-4 text-gray-500">No posts available</p>
                )}
            </div>
        </AppLayout>
    );
}

export default FeedTest;
